# libircmd

​		libircmd是我们自己定义的vdcmd命令格式转换成对应通信协议层可以接收的数据格式。

​		以往版本的libircmd.h中对于命令的归类仅有advanced（非常用）和basic（常用），不便于查找命令和新增命令。故将命令按照功能进行归类排序。

​		此文档对准新版libircmd.h。方便查阅命令。

## 快门相关

| 功能                         | 接口                                |
| ---------------------------- | ----------------------------------- |
| 手动打快门                   | basic_ffc_update                    |
| 设置自动快门状态             | basic_auto_ffc_status_set           |
| 获取自动快门状态             | basic_auto_ffc_status_get           |
| 设置自动ffc参数              | basic_auto_ffc_current_params_set   |
| 获取自动ffc参数              | basic_auto_ffc_current_params_get   |
| 获取自动ffc参数属性          | basic_auto_ffc_params_attribute_get |
| 获取shutter状态              | adv_shutter_status_get              |
| 设置shutter开合状态          | adv_shutter_status_set              |
| 获取当前所有快门功能启用状态 | basic_all_ffc_function_status_get   |
| 设置当前所有快门功能启用状态 | basic_all_ffc_function_status_set   |
| 手动打快门                   | adv_manual_ffc_update               |
| 无快门FFC                    | adv_ffc_without_shutter             |
| 关闭快门片                   | adv_shutter_tab_close               |
| 打开快门片                   | adv_shutter_tab_open                |
| 外部触发无挡                 | adv_external_trigger_shutterless    |

## 设备信息

| 功能             | 接口                  |
| ---------------- | --------------------- |
| 获取设备信息     | basic_device_info_get |
| 获取设备开机时间 | adv_powered_time_get  |

## 机芯状态

| 功能                   | 接口                                |
| ---------------------- | ----------------------------------- |
| 获取模组温度           | basic_device_temp_get               |
| 设置设备实时状态       | adv_device_realtime_status_set      |
| 获取设备实时状态       | adv_device_realtime_status_get      |
| 获取固件log信息        | basic_firmware_log_data_get         |
| 获取当前命令通路类型   | adv_command_channel_type_get        |
| 获取当前设备状态       | basic_device_current_status_get     |
| 感兴趣区域最高温点坐标 | adv_temp_coordinate_of_interest_get |

## 机芯配置

| 功能                 | 接口                           |
| -------------------- | ------------------------------ |
| 设置模组休眠状态     | adv_device_sleep_set           |
| 获取模组休眠状态     | adv_device_sleep_get           |
| 设置设备当前时间     | basic_rtc_current_time_set     |
| 获取设备当前时间     | basic_rtc_current_time_get     |
| DVP/I2C电压切换设置  | adv_voltage_change_set         |
| DVP/I2C电压切换获取  | adv_voltage_change_get         |
| 写FPGA算法参数       | adv_algorithm_parameters_write |
| 读FPGA算法参数       | adv_algorithm_parameters_read  |
| 获取FPGA版本         | adv_fpga_version_get           |
| 打开设备             | adv_device_open                |
| 初始化设备           | adv_device_init                |
| 关闭设备             | adv_device_close               |
| 重启到rom模式            | basic_reset_to_rom             |
| 下载固件             | basic_firmware_download        |
| 进入reboot模式       | adv_enter_reboot_mode          |
| 清空缓存             | adv_cache_clear                |
| 设置波特率           | adv_baudrate_set               |

## 出图配置

| 功能             | 接口                          |
| ---------------- | ----------------------------- |
| 出图             | basic_preview_start           |
| 停图             | basic_preview_stop            |
| 获取出图信息      | basic_preview_info_get        |
| 出码流           | basic_video_stream_continue   |
| 停码流           | basic_video_stream_pause      |
| 设置mipiVC通道   | adv_mipi_channel_type_set     |
| 设置图像数据源   | adv_stream_mid_mode_set       |
| 获得图像数据源   | adv_stream_mid_mode_get       |
| 设置输出帧率     | adv_output_frame_rate_set     |
| 获取输出帧率     | adv_output_frame_rate_get     |
| 设置启动logo状态 | adv_boot_logo_status_set      |
| 获取启动logo状态 | adv_boot_logo_status_get      |
| 设置YUV格式      | adv_yuv_format_set            |
| 获取YUV格式      | adv_yuv_format_get            |
| 设置数字视频输出 | adv_digital_video_output_set  |
| 获取数字视频输出 | adv_digital_video_output_get  |
| 设置模拟视频输出 | adv_analog_video_output_set   |
| 获取模拟视频输出 | adv_analog_video_output_get   |
| 设置画面冻结状态 | adv_picture_freeze_status_set |
| 获取画面冻结状态 | adv_picture_freeze_status_get |
| 获取设备出图信息 | adv_device_preview_info_get   |
| 设置外同步状态   | adv_external_synchronization_set |
| 获取外同步状态   | adv_external_synchronization_get |
| 设置7393接口    | adv_7393_analog_set              |
| 获取7393接口信息| adv_7393_analog_get              |


## 图像格式

| 功能               | 接口                             |
| ------------------ | -------------------------------- |
| 设置镜像和翻转状态 | basic_mirror_and_flip_status_set |
| 获取镜像和翻转状态 | basic_mirror_and_flip_status_get |
| 设置电子变倍-中心  | basic_center_zoom_set            |
| 获取电子变倍-中心  | basic_center_zoom_get            |
| 设置电子变倍-坐标  | adv_coordinate_zoom_set          |
| 获取电子变倍-坐标  | adv_coordinate_zoom_get          |

## 算法参数调节

| 功能                 | 接口                                          |
| -------------------- | --------------------------------------------- |
| 设置图像亮度         | basic_image_brightness_level_set              |
| 获取图像亮度属性     | basic_attribute_of_brightness_level_get       |
| 获取当前亮度等级     | basic_current_brightness_level_get            |
| 设置图像对比度       | basic_image_contrast_level_set                |
| 获取图像对比度属性   | basic_attribute_of_contrast_level_get         |
| 获取图像对比度等级   | basic_current_contrast_level_get              |
| 设置全局对比度       | basic_global_contrast_level_set               |
| 获取全局对比度属性   | basic_attribute_of_global_contrast_level_get  |
| 获取全局对比度等级   | basic_global_contrast_level_get               |
| 设置图像细节增强等级 | basic_image_detail_enhance_level_set          |
| 获得细节增强属性     | basic_attribute_of_detail_enhance_level_get   |
| 获得细节增强等级     | basic_current_detail_enhance_level_get        |
| 设置图像去噪等级     | basic_image_noise_reduction_level_set         |
| 获取图像去噪等级     | basic_current_image_noise_reduction_level_get |
| 获取图像去噪属性     | basic_attribute_of_noise_reduction_level_get  |
| 设置图像ROI等级      | basic_image_roi_level_set                     |
| 获取图像ROI等级      | basic_current_image_roi_level_get             |
| 获取图像ROI属性      | basic_attribute_of_roi_level_get              |
| 设置图像AGC等级      | basic_image_agc_level_set                     |
| 获取图像AGC等级      | basic_current_agc_level_get                   |
| 获取图像AGC属性      | basic_attribute_of_agc_level_get              |
| 设置场景模式         | basic_scene_mode_set                          |
| 获取场景模式属性     | basic_attribute_of_scene_mode_get              |
| 获取场景当前模式     | basic_scene_mode_get                   |
| 设置gamma强度        | basic_gamma_rate_set                          |
| 获取gamma强度        | basic_current_gamma_level_get                 |
| 获取gamma属性        | basic_attribute_of_gamma_level_get            |
| 设置SNR等级          | basic_space_noise_reduce_level_set            |
| 获取SNR等级          | basic_space_noise_reduce_level_get            |
| 获取SNR属性          | basic_space_noise_reduce_attribute_get        |
| 设置TNR等级          | basic_time_noise_reduce_level_set             |
| 获取TNR等级          | basic_time_noise_reduce_level_get             |
| 获取TNR属性          | basic_time_noise_reduce_attribute_get         |
| 设置窗口片补偿       | adv_cel_level_set                             |
| 恢复窗口片补偿       | adv_cel_level_restore                         |
| 设置描边等级         | adv_edge_enhance_set                          |
| 获取描边等级         | adv_edge_enhance_get                          |

## 环境变量修正
| 功能                       | 接口                       |
| -------------------------- | -------------------------- |
| 环境变量修正开关设置        | adv_env_correct_switch_set |
| 环境变量修正开关状态获取    | adv_env_correct_switch_get  |
| 环境反射温度设置            | adv_env_correct_tu_set     |
| 环境反射温度获取            | adv_env_correct_tu_get     |
| 环境大气温度设置            | adv_env_correct_ta_set     |
| 环境大气温度获取            | adv_env_correct_ta_get     |
| 环境大气透过率设置          | adv_env_correct_tau_set    |
| 环境大气透过率获取          | adv_env_correct_tau_get    |
| 目标发射率设置              | adv_env_correct_ems_set    |
| 目标发射率获取              | adv_env_correct_ems_get    |

## 伪彩与等温

| 功能                       | 接口                      |
| -------------------------- | ------------------------- |
| 获取设备支持的伪彩表的总数 | basic_palette_num_get     |
| 获取伪彩名称               | basic_palette_name_get    |
| 获取当前伪彩编号           | basic_palette_idx_get     |
| 设置当前伪彩编号           | basic_palette_idx_set     |
| 获取等温开关               | adv_isothermal_switch_get |
| 设置等温开关               | adv_isothermal_switch_set |
| 获取等温模式               | adv_isothermal_mode_get   |
| 设置等温模式               | adv_isothermal_mode_set   |
| 获取等温分析范围           | adv_isothermal_limit_get  |
| 设置等温分析范围           | adv_isothermal_limit_set  |

## 二次标定相关

| 功能                       | 接口                     |
| -------------------------- | ------------------------ |
| 设置二次标定FLAG使能位状态 | adv_second_cali_flag_set |
| 获取二次标定FLAG使能位状态 | adv_second_cali_flag_get |

## K值标定

| 功能            | 接口                           |
| --------------- | ------------------------------ |
| K值标定         | adv_k_value_calibration        |
| 取消K值标定结果 | adv_k_value_calibration_cancel |
| 清空K值         | adv_k_value_clear              |

## 锅盖标定

| 功能             | 接口                     |
| ---------------- | ------------------------ |
| 自动锅盖标定     | adv_auto_rmcover_cali    |
| 取消锅盖标定结果 | adv_rmcover_calib_cancel |
| 清空锅盖         | adv_rmcover_calib_clear  |

## 盲元标定

| 功能                   | 接口                               |
| ---------------------- | ---------------------------------- |
| 自动盲元标定           | adv_auto_dpc_cali                  |
| 取消该次盲元标定数据   | adv_dpc_calib_data_cancel          |
| 盲元数据清空           | adv_dpc_calib_data_clear           |
| 将光标所在点设为盲元   | adv_set_cursor_position_to_dpc     |
| 将光标所在点设为非盲元 | adv_set_cursor_position_to_non_dpc |
| 光标开关设置           | adv_cursor_switch_status_set       |
| 光标开关获取           | adv_cursor_switch_status_get       |
| 光标位置设置           | adv_cursor_position_set            |
| 光标位置获取           | adv_cursor_position_get            |

## TPD标定

| 功能                | 接口                           |
| ------------------- | ------------------------------ |
| 开始TPD单点标定     | adv_recal_tpd_by_1point        |
| 取消TPD单点标定结果 | adv_recal_tpd_by_1point_cancel |
| 清空TPD单点标定结果 | adv_recal_tpd_by_1point_clear  |
| 开始TPD两点标定     | adv_recal_tpd_by_2point        |
| 取消TPD两点标定结果 | adv_recal_tpd_by_2point_cancel |
| 清空TPD两点标定结果 | adv_recal_tpd_by_2point_clear  |


## 信息行相关

| 功能                    | 接口                    |
| ----------------------- | ----------------------- |
| 直接获取信息行数据(不从帧数据中解析) | adv_info_line_get |
| 设置信息行中index点坐标 | adv_tpd_point_coord_set |
| 设置信息行中index线坐标 | adv_tpd_line_coord_set  |
| 设置信息行中index框坐标 | adv_tpd_rect_coord_set  |

## 测温相关

| 功能                                 | 接口                      |
| ------------------------------------ | ------------------------- |
| 点测温                               | basic_point_temp_info_get |
| 线测温                               | basic_line_temp_info_get  |
| 框测温                               | basic_rect_temp_info_get  |
| 帧测温                               | basic_frame_temp_info_get |
| 在屏幕上显示帧测温最高和最低温点坐标 | adv_show_frame_temp       |
| 在屏幕上显示关注点测温结果           | adv_show_point_temp       |

## 增益相关

| 功能         | 接口           |
| ------------ | -------------- |
| 设置设备增益 | basic_gain_set |
| 获取设备增益 | basic_gain_get |

## 太阳保护

| 功能                           | 接口                                 |
| ------------------------------ | ------------------------------------ |
| 获取太阳保护开关状态           | basic_sun_detect_switch_get          |
| 设置太阳保护开关状态           | basic_sun_detect_switch_set          |
| 获取太阳保护饱和像素比的范围   | basic_sun_detect_pixel_ratio_get     |
| 设置太阳保护饱和像素比的范围   | basic_sun_detect_pixel_ratio_set     |
| 获取当前圆度级别的范围         | basic_sun_detect_roundness_level_get |
| 设置当前圆度级别的范围         | basic_sun_detect_roundness_level_set |
| 设置太阳保护的快门关闭时间阈值 | basic_sun_detect_time_threshold_set  |
| 获得太阳保护的快门关闭时间阈值 | basic_sun_detect_time_threshold_get  |

## 恢复出厂相关

| 功能         | 接口                       |
| ------------ | -------------------------- |
| 恢复出厂数据 | basic_restore_default_data |

## 保存数据相关

| 功能         | 接口            |
| ------------ | --------------- |
| 保存数据相关 | basic_save_data |

# 时间戳

- 2023.8.12 Typed by xianlong.luo，function classified by ling.zhang
  - 根据libir_cmd的dev分支的libircmd.h整理出该文档。

- 2023.9.1 Changed by xianlong.luo
  - Add some new function in Image Format
  - Add libircmd_temp's function，include TPD calibration, info_line, temp_measure

