/***********************************************************************
 * Software License Agreement (BSD License)
 *
 * Copyright 2008-2009  <PERSON> (ma<PERSON><PERSON>@cs.ubc.ca). All rights reserved.
 * Copyright 2008-2009  <PERSON> (<EMAIL>). All rights reserved.
 *
 * THE BSD LICENSE
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
 * NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
 * THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *************************************************************************/

#ifndef OPENCV_FLANN_TIMER_H
#define OPENCV_FLANN_TIMER_H

//! @cond IGNORED

#include <time.h>
#include "opencv2/core.hpp"
#include "opencv2/core/utility.hpp"

namespace cvflann
{

/**
 * A start-stop timer class.
 *
 * Can be used to time portions of code.
 */
class StartStopTimer
{
    int64 startTime;

public:
    /**
     * Value of the timer.
     */
    double value;


    /**
     * Constructor.
     */
    StartStopTimer()
        : startTime(0)
    {
        reset();
    }

    /**
     * Starts the timer.
     */
    void start()
    {
        startTime = cv::getTickCount();
    }

    /**
     * Stops the timer and updates timer value.
     */
    void stop()
    {
        int64 stopTime = cv::getTickCount();
        value += ( (double)stopTime - startTime) / cv::getTickFrequency();
    }

    /**
     * Resets the timer value to 0.
     */
    void reset()
    {
        value = 0;
    }

};

}

//! @endcond

#endif // FLANN_TIMER_H
