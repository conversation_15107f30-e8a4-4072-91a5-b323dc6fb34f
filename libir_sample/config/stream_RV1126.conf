[{"product": "G1280s", "control": {"is_uart_control": true, "is_i2c_control": false, "is_i2c_usb_control": false, "is_usb_control": false, "uart_param": {"com_index": 0}, "i2c_param": {"device_name": "/dev/i2c-1"}, "usb_param": {"pid": "0x00C3", "vid": "0x04B4", "same_id": 0}}, "camera": {"is_auto_image": false, "format": "nv12_and_temp", "width": 1280, "height": 1024, "image_info_height": 1024, "info_line_height": 3, "temp_info_height": 1024, "dummy_info_height": 2, "image_info_ratio": 1.5, "info_line_ratio": 2, "temp_line_ratio": 2, "dummy_info_ratio": 2, "v4l2_stream": [{"device_name": "/dev/video5", "type": "image", "fps": 30, "dev_width": 1280, "dev_height": 771}, {"device_name": "/dev/video6", "type": "temp", "fps": 30, "dev_width": 1280, "dev_height": 1026}], "image_channel_type": "mipi"}}, {"product": "CS640", "control": {"is_uart_control": true, "is_i2c_control": false, "is_i2c_usb_control": false, "is_usb_control": false, "uart_param": {"com_index": 0}, "i2c_param": {"device_name": "/dev/i2c-1"}, "usb_param": {"pid": "0x4012", "vid": "0x3473", "same_id": 0}}, "camera": {"is_auto_image": false, "format": "nv12_and_temp", "width": 640, "height": 512, "image_info_height": 512, "info_line_height": 2, "temp_info_height": 512, "dummy_info_height": 2, "image_info_ratio": 1.5, "info_line_ratio": 2, "temp_line_ratio": 2, "dummy_info_ratio": 2, "v4l2_stream": [{"device_name": "/dev/video38", "type": "image", "fps": 30, "dev_width": 640, "dev_height": 387}, {"device_name": "/dev/video40", "type": "temp", "fps": 30, "dev_width": 640, "dev_height": 514}], "image_channel_type": "usb"}}, {"product": "TC2C", "control": {"is_uart_control": false, "is_i2c_control": true, "is_i2c_usb_control": false, "is_usb_control": false, "uart_param": {"com_index": 0}, "i2c_param": {"device_name": "/dev/i2c-2"}, "usb_param": {"pid": "0x4321", "vid": "0x3474", "same_id": 0}}, "camera": {"is_auto_image": true, "format": "yuyv_and_temp", "width": 256, "height": 192, "image_info_height": 192, "info_line_height": 2, "temp_info_height": 192, "dummy_info_height": 0, "image_info_ratio": 2, "info_line_ratio": 2, "temp_line_ratio": 2, "dummy_info_ratio": 2, "v4l2_stream": [{"device_name": "/dev/video0", "type": "image", "fps": 25, "dev_width": 256, "dev_height": 386}], "image_channel_type": "dvp"}}, {"product": "WN2640", "control": {"is_uart_control": false, "is_i2c_control": true, "is_i2c_usb_control": false, "i2c_param": {"device_name": "/dev/i2c-1"}, "uart_param": {"com_index": 0}}, "camera": {"is_auto_image": false, "format": "yuyv_image", "width": 640, "height": 512, "image_info_height": 512, "info_line_height": 0, "temp_info_height": 0, "dummy_info_height": 0, "image_info_ratio": 2, "info_line_ratio": 2, "temp_line_ratio": 2, "dummy_info_ratio": 2, "v4l2_stream": [{"device_name": "/dev/video5", "type": "image", "fps": 30, "dev_width": 640, "dev_height": 512}], "image_channel_type": "mipi"}}, {"product": "WN2256", "control": {"is_uart_control": false, "is_i2c_control": true, "is_i2c_usb_control": false, "i2c_param": {"device_name": "/dev/i2c-1"}, "uart_param": {"com_index": 0}}, "camera": {"is_auto_image": false, "format": "yuyv_image", "width": 256, "height": 192, "image_info_height": 192, "info_line_height": 0, "temp_info_height": 0, "dummy_info_height": 0, "image_info_ratio": 2, "info_line_ratio": 2, "temp_line_ratio": 2, "dummy_info_ratio": 2, "v4l2_stream": [{"device_name": "/dev/video0", "type": "image", "fps": 50, "dev_width": 256, "dev_height": 192}], "image_channel_type": "dvp"}}, {"product": "WN2384", "control": {"is_uart_control": false, "is_i2c_control": true, "is_i2c_usb_control": false, "i2c_param": {"device_name": "/dev/i2c-1"}, "uart_param": {"com_index": 0}}, "camera": {"is_auto_image": false, "format": "yuyv_image", "width": 384, "height": 288, "image_info_height": 288, "info_line_height": 0, "temp_info_height": 0, "dummy_info_height": 0, "image_info_ratio": 2, "info_line_ratio": 2, "temp_line_ratio": 2, "dummy_info_ratio": 2, "v4l2_stream": [{"device_name": "/dev/video0", "type": "image", "fps": 60, "dev_width": 384, "dev_height": 288}], "image_channel_type": "dvp"}}, {"product": "WN2384T", "control": {"is_uart_control": false, "is_i2c_control": true, "is_i2c_usb_control": false, "i2c_param": {"device_name": "/dev/i2c-1"}, "uart_param": {"com_index": 0}}, "camera": {"is_auto_image": true, "format": "yuyv_and_temp", "width": 384, "height": 288, "image_info_height": 288, "info_line_height": 2, "temp_info_height": 288, "dummy_info_height": 0, "image_info_ratio": 2, "info_line_ratio": 2, "temp_line_ratio": 2, "dummy_info_ratio": 2, "v4l2_stream": [{"device_name": "/dev/video5", "type": "image", "fps": 30, "dev_width": 384, "dev_height": 578}], "image_channel_type": "mipi"}}]