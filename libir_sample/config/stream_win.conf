[{"product": "G1280s", "control": {"is_uart_control": true, "is_i2c_control": false, "is_i2c_usb_control": false, "is_usb_control": false, "uart_param": {"com_index": 0}, "i2c_param": {"device_name": "/dev/i2c-1"}, "usb_param": {"pid": "0x00C3", "vid": "0x04B4", "same_id": 0}}, "camera": {"is_auto_image": false, "format": "nv12_and_temp", "width": 1280, "height": 1024, "image_info_height": 1024, "info_line_height": 3, "temp_info_height": 1024, "dummy_info_height": 2, "image_info_ratio": 1.5, "info_line_ratio": 2, "temp_line_ratio": 2, "dummy_info_ratio": 2, "uvc_stream": {"device_info": {"pid": "0x00C3", "vid": "0x04B4", "same_id": 0}, "width": 1280, "height": 1797, "fps": 30, "frame_size_ratio": 2}}}, {"product": "CS640", "control": {"is_uart_control": true, "is_i2c_control": false, "is_i2c_usb_control": false, "is_usb_control": false, "uart_param": {"com_index": 0}, "i2c_param": {"device_name": "/dev/i2c-1"}, "usb_param": {"pid": "0x4012", "vid": "0x3473", "same_id": 0}}, "camera": {"is_auto_image": false, "format": "nv12_and_temp", "width": 640, "height": 512, "image_info_height": 512, "info_line_height": 2, "temp_info_height": 512, "dummy_info_height": 2, "image_info_ratio": 1.5, "info_line_ratio": 2, "temp_line_ratio": 2, "dummy_info_ratio": 2, "uvc_stream": {"device_info": {"pid": "0x4012", "vid": "0x3474", "same_id": 0}, "width": 640, "height": 1200, "fps": 30, "frame_size_ratio": 1.5}}}, {"product": "P2L", "control": {"is_uart_control": false, "is_i2c_control": false, "is_i2c_usb_control": false, "is_usb_control": true, "uart_param": {"com_index": 0}, "i2c_param": {"device_name": "/dev/i2c-1"}, "usb_param": {"pid": "0x4281", "vid": "0x3474", "same_id": 0}}, "camera": {"is_auto_image": true, "format": "yuyv_and_temp", "width": 256, "height": 192, "image_info_height": 192, "info_line_height": 2, "temp_info_height": 192, "dummy_info_height": 0, "image_info_ratio": 2, "info_line_ratio": 2, "temp_line_ratio": 2, "dummy_info_ratio": 2, "uvc_stream": {"device_info": {"pid": "0x4281", "vid": "0x3474", "same_id": 0}, "width": 256, "height": 386, "fps": 25, "frame_size_ratio": 2}}}, {"product": "TC2C", "control": {"is_uart_control": false, "is_i2c_control": false, "is_i2c_usb_control": false, "is_usb_control": true, "uart_param": {"com_index": 0}, "i2c_param": {"device_name": "/dev/i2c-1"}, "usb_param": {"pid": "0x4321", "vid": "0x3474", "same_id": 0}}, "camera": {"is_auto_image": true, "format": "yuyv_and_temp", "width": 256, "height": 192, "image_info_height": 192, "info_line_height": 2, "temp_info_height": 192, "dummy_info_height": 0, "image_info_ratio": 2, "info_line_ratio": 2, "temp_line_ratio": 2, "dummy_info_ratio": 2, "uvc_stream": {"device_info": {"pid": "0x4321", "vid": "0x3474", "same_id": 0}, "width": 256, "height": 386, "fps": 25, "frame_size_ratio": 2}}}, {"product": "AC02", "control": {"is_uart_control": false, "is_i2c_control": false, "is_i2c_usb_control": false, "is_usb_control": true, "uart_param": {"com_index": 0}, "i2c_param": {"device_name": "/dev/i2c-1"}, "usb_param": {"pid": "0x0020", "vid": "0x3474", "same_id": 0}}, "camera": {"is_auto_image": true, "format": "yuyv_image", "width": 640, "height": 512, "image_info_height": 512, "info_line_height": 0, "temp_info_height": 0, "dummy_info_height": 0, "image_info_ratio": 2, "info_line_ratio": 2, "temp_line_ratio": 2, "dummy_info_ratio": 2, "uvc_stream": {"device_info": {"pid": "0x0020", "vid": "0x3474", "same_id": 0}, "width": 640, "height": 512, "fps": 25, "frame_size_ratio": 2}}}, {"product": "WN2384", "control": {"is_uart_control": false, "is_i2c_control": false, "is_i2c_usb_control": false, "is_usb_control": true, "uart_param": {"com_index": 0}, "i2c_param": {"device_name": "/dev/i2c-1"}, "usb_param": {"pid": "0x43D1", "vid": "0x3474", "same_id": 0}}, "camera": {"is_auto_image": false, "format": "yuyv_image", "width": 384, "height": 288, "image_info_height": 288, "info_line_height": 0, "temp_info_height": 0, "dummy_info_height": 0, "image_info_ratio": 2, "info_line_ratio": 2, "temp_line_ratio": 2, "dummy_info_ratio": 2, "uvc_stream": {"device_info": {"pid": "0x0020", "vid": "0x3474", "same_id": 0}, "width": 384, "height": 288, "fps": 30, "frame_size_ratio": 2}}}, {"product": "WN2256", "control": {"is_uart_control": false, "is_i2c_control": false, "is_i2c_usb_control": false, "is_usb_control": true, "uart_param": {"com_index": 0}, "i2c_param": {"device_name": "/dev/i2c-1"}, "usb_param": {"pid": "0x43C1", "vid": "0x3474", "same_id": 0}}, "camera": {"is_auto_image": false, "format": "yuyv_image", "width": 256, "height": 192, "image_info_height": 192, "info_line_height": 0, "temp_info_height": 0, "dummy_info_height": 0, "image_info_ratio": 2, "info_line_ratio": 2, "temp_line_ratio": 2, "dummy_info_ratio": 2, "uvc_stream": {"device_info": {"pid": "0x0020", "vid": "0x3474", "same_id": 0}, "width": 256, "height": 192, "fps": 50, "frame_size_ratio": 2}}}, {"product": "WN2640", "control": {"is_uart_control": false, "is_i2c_control": false, "is_i2c_usb_control": false, "is_usb_control": true, "uart_param": {"com_index": 0}, "i2c_param": {"device_name": "/dev/i2c-1"}, "usb_param": {"pid": "0x43E1", "vid": "0x3474", "same_id": 0}}, "camera": {"is_auto_image": false, "format": "yuyv_image", "width": 640, "height": 512, "image_info_height": 512, "info_line_height": 0, "temp_info_height": 0, "dummy_info_height": 0, "image_info_ratio": 2, "info_line_ratio": 2, "temp_line_ratio": 2, "dummy_info_ratio": 2, "uvc_stream": {"device_info": {"pid": "0x0020", "vid": "0x3474", "same_id": 0}, "width": 640, "height": 512, "fps": 30, "frame_size_ratio": 2}}}, {"product": "WN2384T", "control": {"is_uart_control": false, "is_i2c_control": false, "is_i2c_usb_control": false, "is_usb_control": true, "uart_param": {"com_index": 0}, "i2c_param": {"device_name": "/dev/i2c-1"}, "usb_param": {"pid": "0x43D1", "vid": "0x3474", "same_id": 0}}, "camera": {"is_auto_image": true, "format": "yuyv_and_temp", "width": 384, "height": 288, "image_info_height": 288, "info_line_height": 2, "temp_info_height": 288, "dummy_info_height": 0, "image_info_ratio": 2, "info_line_ratio": 2, "temp_line_ratio": 2, "dummy_info_ratio": 2, "uvc_stream": {"device_info": {"pid": "0x43D1", "vid": "0x3474", "same_id": 0}, "width": 384, "height": 578, "fps": 30, "frame_size_ratio": 2}}}]