﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="头文件\drivers">
      <UniqueIdentifier>{23b66746-e62b-4e45-b21b-86b0b26e39d5}</UniqueIdentifier>
    </Filter>
    <Filter Include="头文件\interfaces">
      <UniqueIdentifier>{9f30df72-95a0-4887-b696-11c9a2efa1c0}</UniqueIdentifier>
    </Filter>
    <Filter Include="源文件\common">
      <UniqueIdentifier>{4105451b-7ddc-4e9c-8331-4598a493dcb3}</UniqueIdentifier>
    </Filter>
    <Filter Include="源文件\components">
      <UniqueIdentifier>{84db1809-fd9d-4bc9-8c5c-a30474d11927}</UniqueIdentifier>
    </Filter>
    <Filter Include="头文件\other">
      <UniqueIdentifier>{13257f8f-d957-408f-96c5-fbfdf747c3c6}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\include\sample.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\drivers\libiruart.h">
      <Filter>头文件\drivers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\drivers\libiruvc.h">
      <Filter>头文件\drivers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\interfaces\libircam.h">
      <Filter>头文件\interfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\interfaces\libircmd.h">
      <Filter>头文件\interfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\interfaces\libircmd_temp.h">
      <Filter>头文件\interfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\drivers\libirdfu.h">
      <Filter>头文件\drivers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\other\libirupgrade.h">
      <Filter>头文件\other</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\src\sample.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\common\data.cpp">
      <Filter>源文件\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\components\cmd.cpp">
      <Filter>源文件\components</Filter>
    </ClCompile>
  </ItemGroup>
</Project>