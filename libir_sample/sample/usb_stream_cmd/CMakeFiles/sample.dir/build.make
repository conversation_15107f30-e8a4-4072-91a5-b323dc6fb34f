# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/cmake/bin/cmake

# The command to remove a file.
RM = /opt/cmake/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd

# Include any dependencies generated for this target.
include CMakeFiles/sample.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/sample.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/sample.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/sample.dir/flags.make

CMakeFiles/sample.dir/codegen:
.PHONY : CMakeFiles/sample.dir/codegen

CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.o: CMakeFiles/sample.dir/flags.make
CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.o: /home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c
CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.o: CMakeFiles/sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.o"
	aarch64-linux-gnu-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.o -MF CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.o.d -o CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.o -c /home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c

CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.i"
	aarch64-linux-gnu-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c > CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.i

CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.s"
	aarch64-linux-gnu-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c -o CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.s

CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.o: CMakeFiles/sample.dir/flags.make
CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.o: /home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/config.cpp
CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.o: CMakeFiles/sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.o"
	aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.o -MF CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.o.d -o CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.o -c /home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/config.cpp

CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.i"
	aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/config.cpp > CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.i

CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.s"
	aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/config.cpp -o CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.s

CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.o: CMakeFiles/sample.dir/flags.make
CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.o: /home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/data.cpp
CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.o: CMakeFiles/sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.o"
	aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.o -MF CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.o.d -o CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.o -c /home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/data.cpp

CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.i"
	aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/data.cpp > CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.i

CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.s"
	aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/data.cpp -o CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.s

CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.o: CMakeFiles/sample.dir/flags.make
CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.o: /home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp
CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.o: CMakeFiles/sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.o"
	aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.o -MF CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.o.d -o CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.o -c /home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp

CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.i"
	aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp > CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.i

CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.s"
	aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp -o CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.s

CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.o: CMakeFiles/sample.dir/flags.make
CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.o: /home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp
CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.o: CMakeFiles/sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.o"
	aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.o -MF CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.o.d -o CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.o -c /home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp

CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.i"
	aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp > CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.i

CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.s"
	aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp -o CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.s

CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.o: CMakeFiles/sample.dir/flags.make
CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.o: /home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp
CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.o: CMakeFiles/sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.o"
	aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.o -MF CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.o.d -o CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.o -c /home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp

CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.i"
	aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp > CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.i

CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.s"
	aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp -o CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.s

CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.o: CMakeFiles/sample.dir/flags.make
CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.o: /home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp
CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.o: CMakeFiles/sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.o"
	aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.o -MF CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.o.d -o CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.o -c /home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp

CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.i"
	aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp > CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.i

CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.s"
	aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp -o CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.s

CMakeFiles/sample.dir/src/sample.cpp.o: CMakeFiles/sample.dir/flags.make
CMakeFiles/sample.dir/src/sample.cpp.o: src/sample.cpp
CMakeFiles/sample.dir/src/sample.cpp.o: CMakeFiles/sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/sample.dir/src/sample.cpp.o"
	aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/sample.dir/src/sample.cpp.o -MF CMakeFiles/sample.dir/src/sample.cpp.o.d -o CMakeFiles/sample.dir/src/sample.cpp.o -c /home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/src/sample.cpp

CMakeFiles/sample.dir/src/sample.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sample.dir/src/sample.cpp.i"
	aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/src/sample.cpp > CMakeFiles/sample.dir/src/sample.cpp.i

CMakeFiles/sample.dir/src/sample.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sample.dir/src/sample.cpp.s"
	aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/src/sample.cpp -o CMakeFiles/sample.dir/src/sample.cpp.s

# Object files for target sample
sample_OBJECTS = \
"CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.o" \
"CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.o" \
"CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.o" \
"CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.o" \
"CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.o" \
"CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.o" \
"CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.o" \
"CMakeFiles/sample.dir/src/sample.cpp.o"

# External object files for target sample
sample_EXTERNAL_OBJECTS =

sample: CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.o
sample: CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.o
sample: CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.o
sample: CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.o
sample: CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.o
sample: CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.o
sample: CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.o
sample: CMakeFiles/sample.dir/src/sample.cpp.o
sample: CMakeFiles/sample.dir/build.make
sample: CMakeFiles/sample.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Linking CXX executable sample"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/sample.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/sample.dir/build: sample
.PHONY : CMakeFiles/sample.dir/build

CMakeFiles/sample.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/sample.dir/cmake_clean.cmake
.PHONY : CMakeFiles/sample.dir/clean

CMakeFiles/sample.dir/depend:
	cd /home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd /home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd /home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd /home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd /home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/CMakeFiles/sample.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/sample.dir/depend

