CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.o: \
 /home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/stdc-predef.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/string.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/libc-header-start.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/features.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/sys/cdefs.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/wordsize.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/long-double.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/gnu/stubs.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/gnu/stubs-lp64.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/lib/gcc/aarch64-linux-gnu/7.5.0/include/stddef.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/xlocale.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/stdio.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/types.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/typesizes.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/libio.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/_G_config.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/wchar.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/lib/gcc/aarch64-linux-gnu/7.5.0/include/stdarg.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/stdio_lim.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/sys_errlist.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/math.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/math-vector.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/libm-simd-decl-stubs.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/huge_val.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/huge_valf.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/huge_vall.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/inf.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/nan.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/flt-eval-method.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/fp-logb.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/fp-fast.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/mathcalls.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/stdlib.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/waitflags.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/waitstatus.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/sys/types.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/types/clock_t.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/types/clockid_t.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/types/time_t.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/types/timer_t.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/endian.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/endian.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/byteswap.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/byteswap-16.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/uintn-identity.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/sys/select.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/select.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/sigset.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/types/struct_timeval.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/types/struct_timespec.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/sys/sysmacros.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/sysmacros.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/pthreadtypes.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/alloca.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/stdlib-float.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/lib/gcc/aarch64-linux-gnu/7.5.0/include-fixed/limits.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/lib/gcc/aarch64-linux-gnu/7.5.0/include-fixed/syslimits.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/limits.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/posix1_lim.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/local_lim.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/linux/limits.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/bits/posix2_lim.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/aarch64-linux-gnu/libc/usr/include/ctype.h \
 /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/lib/gcc/aarch64-linux-gnu/7.5.0/include/float.h \
 /home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/../../thirdparty/cJSON/include/cJSON.h
