# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# compile C with aarch64-linux-gnu-gcc
# compile CXX with aarch64-linux-gnu-g++
C_DEFINES = 

C_INCLUDES = -I/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/../../interfaces -I/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/../../drivers -I/usr/include/opencv4 -I/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/../../common -I/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/../../other -I/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/../../components -I/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/../../thirdparty/cJSON/include -I/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/../../thirdparty -I/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/./include -I/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/.

C_FLAGS =  -g -s -Wno-incompatible-pointer-types -Wmissing-field-initializers -Wsign-compare

CXX_DEFINES = 

CXX_INCLUDES = -I/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/../../interfaces -I/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/../../drivers -I/usr/include/opencv4 -I/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/../../common -I/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/../../other -I/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/../../components -I/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/../../thirdparty/cJSON/include -I/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/../../thirdparty -I/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/./include -I/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/.

CXX_FLAGS =  -g -s -Wmissing-field-initializers -Wsign-compare

