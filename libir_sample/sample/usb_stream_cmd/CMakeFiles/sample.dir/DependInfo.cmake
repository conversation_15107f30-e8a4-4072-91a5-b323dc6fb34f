
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c" "CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.o" "gcc" "CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.o.d"
  "/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/config.cpp" "CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.o" "gcc" "CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.o.d"
  "/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/data.cpp" "CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.o" "gcc" "CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.o.d"
  "/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp" "CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.o" "gcc" "CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.o.d"
  "/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp" "CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.o" "gcc" "CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.o.d"
  "/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp" "CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.o" "gcc" "CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.o.d"
  "/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp" "CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.o" "gcc" "CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.o.d"
  "/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/src/sample.cpp" "CMakeFiles/sample.dir/src/sample.cpp.o" "gcc" "CMakeFiles/sample.dir/src/sample.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
