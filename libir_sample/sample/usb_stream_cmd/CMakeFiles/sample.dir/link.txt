aarch64-linux-gnu-g++  -g -s -Wmissing-field-initializers -Wsign-compare -rdynamic CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.o CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.o CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.o CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.o CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.o CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.o CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.o CMakeFiles/sample.dir/src/sample.cpp.o -o sample   -L/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/../../drivers  -L/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/../../thirdparty  -L/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/build  -Wl,-rpath,/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/../../drivers:/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/../../thirdparty:/home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/build -lircmd -liruart -liruvc -lircam -lirtemp -lirparse -lpthread -lusb-1.0 -lopencv_highgui -lopencv_imgcodecs -lopencv_imgproc -lopencv_core -lm
