# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/cmake/bin/cmake

# The command to remove a file.
RM = /opt/cmake/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/opt/cmake/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/opt/cmake/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/CMakeFiles /home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/AC20_SDK/linux_sdk/libir_sample/sample/usb_stream_cmd/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named sample

# Build rule for target.
sample: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sample
.PHONY : sample

# fast build rule for target.
sample/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sample.dir/build.make CMakeFiles/sample.dir/build
.PHONY : sample/fast

home/aaron/AC20_SDK/linux_sdk/libir_sample/common/config.o: home/aaron/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.o
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/common/config.o

# target to build an object file
home/aaron/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sample.dir/build.make CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.o
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.o

home/aaron/AC20_SDK/linux_sdk/libir_sample/common/config.i: home/aaron/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.i
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/common/config.i

# target to preprocess a source file
home/aaron/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sample.dir/build.make CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.i
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.i

home/aaron/AC20_SDK/linux_sdk/libir_sample/common/config.s: home/aaron/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.s
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/common/config.s

# target to generate assembly for a file
home/aaron/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sample.dir/build.make CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.s
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/common/config.cpp.s

home/aaron/AC20_SDK/linux_sdk/libir_sample/common/data.o: home/aaron/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.o
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/common/data.o

# target to build an object file
home/aaron/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sample.dir/build.make CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.o
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.o

home/aaron/AC20_SDK/linux_sdk/libir_sample/common/data.i: home/aaron/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.i
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/common/data.i

# target to preprocess a source file
home/aaron/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sample.dir/build.make CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.i
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.i

home/aaron/AC20_SDK/linux_sdk/libir_sample/common/data.s: home/aaron/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.s
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/common/data.s

# target to generate assembly for a file
home/aaron/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sample.dir/build.make CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.s
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/common/data.cpp.s

home/aaron/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.o: home/aaron/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.o
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.o

# target to build an object file
home/aaron/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sample.dir/build.make CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.o
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.o

home/aaron/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.i: home/aaron/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.i
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.i

# target to preprocess a source file
home/aaron/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sample.dir/build.make CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.i
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.i

home/aaron/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.s: home/aaron/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.s
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.s

# target to generate assembly for a file
home/aaron/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sample.dir/build.make CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.s
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.cpp.s

home/aaron/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.o: home/aaron/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.o
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.o

# target to build an object file
home/aaron/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sample.dir/build.make CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.o
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.o

home/aaron/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.i: home/aaron/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.i
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.i

# target to preprocess a source file
home/aaron/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sample.dir/build.make CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.i
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.i

home/aaron/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.s: home/aaron/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.s
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.s

# target to generate assembly for a file
home/aaron/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sample.dir/build.make CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.s
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.cpp.s

home/aaron/AC20_SDK/linux_sdk/libir_sample/components/cmd.o: home/aaron/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.o
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/components/cmd.o

# target to build an object file
home/aaron/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sample.dir/build.make CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.o
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.o

home/aaron/AC20_SDK/linux_sdk/libir_sample/components/cmd.i: home/aaron/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.i
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/components/cmd.i

# target to preprocess a source file
home/aaron/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sample.dir/build.make CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.i
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.i

home/aaron/AC20_SDK/linux_sdk/libir_sample/components/cmd.s: home/aaron/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.s
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/components/cmd.s

# target to generate assembly for a file
home/aaron/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sample.dir/build.make CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.s
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/components/cmd.cpp.s

home/aaron/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.o: home/aaron/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.o
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.o

# target to build an object file
home/aaron/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sample.dir/build.make CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.o
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.o

home/aaron/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.i: home/aaron/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.i
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.i

# target to preprocess a source file
home/aaron/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sample.dir/build.make CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.i
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.i

home/aaron/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.s: home/aaron/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.s
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.s

# target to generate assembly for a file
home/aaron/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sample.dir/build.make CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.s
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.cpp.s

home/aaron/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.o: home/aaron/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.o
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.o

# target to build an object file
home/aaron/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sample.dir/build.make CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.o
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.o

home/aaron/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.i: home/aaron/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.i
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.i

# target to preprocess a source file
home/aaron/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sample.dir/build.make CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.i
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.i

home/aaron/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.s: home/aaron/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.s
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.s

# target to generate assembly for a file
home/aaron/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sample.dir/build.make CMakeFiles/sample.dir/home/<USER>/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.s
.PHONY : home/aaron/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.c.s

src/sample.o: src/sample.cpp.o
.PHONY : src/sample.o

# target to build an object file
src/sample.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sample.dir/build.make CMakeFiles/sample.dir/src/sample.cpp.o
.PHONY : src/sample.cpp.o

src/sample.i: src/sample.cpp.i
.PHONY : src/sample.i

# target to preprocess a source file
src/sample.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sample.dir/build.make CMakeFiles/sample.dir/src/sample.cpp.i
.PHONY : src/sample.cpp.i

src/sample.s: src/sample.cpp.s
.PHONY : src/sample.s

# target to generate assembly for a file
src/sample.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sample.dir/build.make CMakeFiles/sample.dir/src/sample.cpp.s
.PHONY : src/sample.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... sample"
	@echo "... home/aaron/AC20_SDK/linux_sdk/libir_sample/common/config.o"
	@echo "... home/aaron/AC20_SDK/linux_sdk/libir_sample/common/config.i"
	@echo "... home/aaron/AC20_SDK/linux_sdk/libir_sample/common/config.s"
	@echo "... home/aaron/AC20_SDK/linux_sdk/libir_sample/common/data.o"
	@echo "... home/aaron/AC20_SDK/linux_sdk/libir_sample/common/data.i"
	@echo "... home/aaron/AC20_SDK/linux_sdk/libir_sample/common/data.s"
	@echo "... home/aaron/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.o"
	@echo "... home/aaron/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.i"
	@echo "... home/aaron/AC20_SDK/linux_sdk/libir_sample/common/opencv_display.s"
	@echo "... home/aaron/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.o"
	@echo "... home/aaron/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.i"
	@echo "... home/aaron/AC20_SDK/linux_sdk/libir_sample/common/uvc_camera.s"
	@echo "... home/aaron/AC20_SDK/linux_sdk/libir_sample/components/cmd.o"
	@echo "... home/aaron/AC20_SDK/linux_sdk/libir_sample/components/cmd.i"
	@echo "... home/aaron/AC20_SDK/linux_sdk/libir_sample/components/cmd.s"
	@echo "... home/aaron/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.o"
	@echo "... home/aaron/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.i"
	@echo "... home/aaron/AC20_SDK/linux_sdk/libir_sample/components/temp_measure.s"
	@echo "... home/aaron/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.o"
	@echo "... home/aaron/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.i"
	@echo "... home/aaron/AC20_SDK/linux_sdk/libir_sample/thirdparty/cJSON/src/cJSON.s"
	@echo "... src/sample.o"
	@echo "... src/sample.i"
	@echo "... src/sample.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

