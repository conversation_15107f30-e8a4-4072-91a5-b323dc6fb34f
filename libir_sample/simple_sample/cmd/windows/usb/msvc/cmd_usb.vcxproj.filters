﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\libir_SDK_release\include\error.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\libir_SDK_release\include\libir_infoparse.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\libir_SDK_release\include\libircam.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\libir_SDK_release\include\libircmd.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\libir_SDK_release\include\libircmd_temp.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\libir_SDK_release\include\libirdfu.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\libir_SDK_release\include\libiri2c.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\libir_SDK_release\include\libirparse.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\libir_SDK_release\include\libirtemp.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\libir_SDK_release\include\libiruart.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\libir_SDK_release\include\libirupgrade.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\libir_SDK_release\include\libiruvc.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\libir_SDK_release\include\libirv4l2.h">
      <Filter>头文件</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\sample.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
  </ItemGroup>
</Project>