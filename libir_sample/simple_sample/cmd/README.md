# 简介  
当前路径下的示例展示了Windows和Linux平台下的命令下发功能  
示例中会下发两个命令，分别是获取设备名称信息和打快门  

# 目录结构
```
cmd/
│
├─linux/
│  ├─i2c/
│  └─uart/
│
├─windows/
│  ├─usb/
│  ├─usb-i2c/
│  └─uart/
│
└─README.md
```
# 示例支持的平台及对应的协议
## Windows平台  
支持**usb**, **usb-i2c**, **uart**共三种协议，其中usb-i2c指的是通过赛普拉斯转板，将usb接口转换为i2c接口  
## Linux平台  
支持**I2C**, **UART**共两种协议  

> **WARNING**: 模组不一定支持全部的控制方式，需要根据模组的能力和自身的需求进行选择
> 
> **Tips**: 展示出来的五种组合仅为最常使用的组合，还有其他的控制方式，例如Linux平台下也可以使用usb进行控制
