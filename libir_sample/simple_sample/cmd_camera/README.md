# 简介  
当前路径下的示例展示了Windows和Linux平台下的出图功能  
其中Windows平台采用opencv进行出图显示  
Linux平台不通过图像的方式显示，而是保存出图的第一帧数据，用户可以通过相关软件进行查看  

# 目录结构
```
cmd_camera/
│
├─linux/
│  ├─uvc_usb
│  └─v4l2_i2c
│
├─windows/
|  └─uvc_usb
│
└─README.md
```
# 示例支持的平台及对应的协议
## Linux平台  
使用基于usb的uvc协议进行命令通道和视频通道的传输  
使用i2c进行命令通道的传输，使用v4l2进行视频通道的传输  
## Windows平台  
使用基于usb的uvc协议进行命令通道和视频通道的传输  

> **WARNING**: 模组不一定支持全部的控制方式，需要根据模组的能力和自身的需求进行选择  
> 
> **Tips**: 除了展示出来的两种示例外，还有其他的控制方式。如果代码无法满足您的需求，请参看当前目录的代码和cmd目录的代码以及相关的头文件进行适当的修改。  

# 出图需要的相关信息
1. 命令通道的协议类型 (uart, i2c, usb, ...)
2. 命令通道使用的协议类型及其相关信息 (比如USB需要PID、UID、SAMEID，uart需要com index，...)
3. 视频通道的协议类型 (usb, usb-i2c, v4l2)
4. 是否自动出图 (true, false)
5. 出图格式 (USB, MPI, DVP, ...)
6. 图像类型 ("YUYV", "UYVU", "NV12")
7. 出单图像，出单温度，出双图 (对应枚举video_output_num_e结构)
8. 宽度 (整体数据帧、图像数据、图像信息行、温度数据共用一个宽度)
9.  数据高度 (图像数据高度和温度数据高度共用一个高度)
10. 图像信息行高度
11. 温度信息行高度
12. 图像数据系数 (一般使用YUYV的时候，这个值为2，表明一个数据需要两字节数据存储，下面同理)
13. 温度数据系数 (一般使用YUYV的时候，这个值为2)
14. 图像信息行数据系数 (一般使用YUYV的时候，这个值为2)
15. 温度信息行数据系数 (一般使用YUYV的时候，这个值为2)
16. Linux平台下使用V4L2出图时需要设备名字及对应的数据类型 (如"dev/video5, image")  

> 其余使用到的信息均采用上述信息计算出来  
> 如V4L2出图时配置的，完整一帧图像的宽 = 所有数据公用的宽(上边第八点的宽度)，高 = 图像数据高 + 温度数据高 + 信息行数据高  
> **WARNING**：此高度计算针对一个像素点使用两个字节表示的情况（即12-15中的提到的数据系数），如果图像数据系数为1.5，温度数据系数为2，信息行系数为2，则高 = 图像数据高\*1.5/2 + 图像数据高\*2/2 + 信息行数据高\*2/2
