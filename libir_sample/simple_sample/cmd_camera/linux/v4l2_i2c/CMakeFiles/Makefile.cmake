# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "CMakeFiles/3.31.6/CMakeCCompiler.cmake"
  "CMakeFiles/3.31.6/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.31.6/CMakeSystem.cmake"
  "CMakeLists.txt"
  "extern_lib.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/CMakeCInformation.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/CMakeCXXInformation.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/CMakeGenericSystem.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/CMakeInitializeConfigs.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/CMakeSystemSpecificInformation.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/CMakeSystemSpecificInitialize.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/Compiler/GNU-C.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/Compiler/GNU-CXX.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/Compiler/GNU.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/Internal/CMakeCLinkerInformation.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/Internal/CMakeCXXLinkerInformation.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/Linker/GNU-C.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/Linker/GNU-CXX.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/Linker/GNU.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/Platform/Linker/GNU.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/Platform/Linker/Linux-GNU-C.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/Platform/Linker/Linux-GNU-CXX.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/Platform/Linker/Linux-GNU.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/Platform/Linux-GNU-C.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/Platform/Linux-GNU-CXX.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/Platform/Linux-GNU.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/Platform/Linux-Initialize.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/Platform/Linux.cmake"
  "/opt/cmake/share/cmake-3.31/Modules/Platform/UnixPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/sample_cmd_camera_linux.dir/DependInfo.cmake"
  )
