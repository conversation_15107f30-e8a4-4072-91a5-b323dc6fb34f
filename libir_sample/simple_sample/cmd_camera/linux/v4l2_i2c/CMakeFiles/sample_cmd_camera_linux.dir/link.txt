/opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/bin/aarch64-linux-gnu-g++  -s -rdynamic CMakeFiles/sample_cmd_camera_linux.dir/sample.cpp.o -o sample_cmd_camera_linux   -L/home/<USER>/AC20_SDK/linux_sdk/libir_sample/simple_sample/cmd_camera/linux/v4l2_i2c/../../../../../libir_SDK_release/linux/aarch64-linux-gnu  -L/home/<USER>/AC20_SDK/linux_sdk/libir_sample/simple_sample/cmd_camera/linux/v4l2_i2c/../../../../libs/linux/aarch64-linux-gnu  -Wl,-rpath,/home/<USER>/AC20_SDK/linux_sdk/libir_sample/simple_sample/cmd_camera/linux/v4l2_i2c/../../../../../libir_SDK_release/linux/aarch64-linux-gnu:/home/<USER>/AC20_SDK/linux_sdk/libir_sample/simple_sample/cmd_camera/linux/v4l2_i2c/../../../../libs/linux/aarch64-linux-gnu -lircam -lircmd -liri2c -lirv4l2 -lm
