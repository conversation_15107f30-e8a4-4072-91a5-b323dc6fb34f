# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/cmake/bin/cmake

# The command to remove a file.
RM = /opt/cmake/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/AC20_SDK/linux_sdk/libir_sample/simple_sample/cmd_camera/linux/v4l2_i2c

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/AC20_SDK/linux_sdk/libir_sample/simple_sample/cmd_camera/linux/v4l2_i2c

# Include any dependencies generated for this target.
include CMakeFiles/sample_cmd_camera_linux.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/sample_cmd_camera_linux.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/sample_cmd_camera_linux.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/sample_cmd_camera_linux.dir/flags.make

CMakeFiles/sample_cmd_camera_linux.dir/codegen:
.PHONY : CMakeFiles/sample_cmd_camera_linux.dir/codegen

CMakeFiles/sample_cmd_camera_linux.dir/sample.cpp.o: CMakeFiles/sample_cmd_camera_linux.dir/flags.make
CMakeFiles/sample_cmd_camera_linux.dir/sample.cpp.o: sample.cpp
CMakeFiles/sample_cmd_camera_linux.dir/sample.cpp.o: CMakeFiles/sample_cmd_camera_linux.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/AC20_SDK/linux_sdk/libir_sample/simple_sample/cmd_camera/linux/v4l2_i2c/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/sample_cmd_camera_linux.dir/sample.cpp.o"
	/opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/sample_cmd_camera_linux.dir/sample.cpp.o -MF CMakeFiles/sample_cmd_camera_linux.dir/sample.cpp.o.d -o CMakeFiles/sample_cmd_camera_linux.dir/sample.cpp.o -c /home/<USER>/AC20_SDK/linux_sdk/libir_sample/simple_sample/cmd_camera/linux/v4l2_i2c/sample.cpp

CMakeFiles/sample_cmd_camera_linux.dir/sample.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sample_cmd_camera_linux.dir/sample.cpp.i"
	/opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/AC20_SDK/linux_sdk/libir_sample/simple_sample/cmd_camera/linux/v4l2_i2c/sample.cpp > CMakeFiles/sample_cmd_camera_linux.dir/sample.cpp.i

CMakeFiles/sample_cmd_camera_linux.dir/sample.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sample_cmd_camera_linux.dir/sample.cpp.s"
	/opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/AC20_SDK/linux_sdk/libir_sample/simple_sample/cmd_camera/linux/v4l2_i2c/sample.cpp -o CMakeFiles/sample_cmd_camera_linux.dir/sample.cpp.s

# Object files for target sample_cmd_camera_linux
sample_cmd_camera_linux_OBJECTS = \
"CMakeFiles/sample_cmd_camera_linux.dir/sample.cpp.o"

# External object files for target sample_cmd_camera_linux
sample_cmd_camera_linux_EXTERNAL_OBJECTS =

sample_cmd_camera_linux: CMakeFiles/sample_cmd_camera_linux.dir/sample.cpp.o
sample_cmd_camera_linux: CMakeFiles/sample_cmd_camera_linux.dir/build.make
sample_cmd_camera_linux: CMakeFiles/sample_cmd_camera_linux.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/AC20_SDK/linux_sdk/libir_sample/simple_sample/cmd_camera/linux/v4l2_i2c/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable sample_cmd_camera_linux"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/sample_cmd_camera_linux.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/sample_cmd_camera_linux.dir/build: sample_cmd_camera_linux
.PHONY : CMakeFiles/sample_cmd_camera_linux.dir/build

CMakeFiles/sample_cmd_camera_linux.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/sample_cmd_camera_linux.dir/cmake_clean.cmake
.PHONY : CMakeFiles/sample_cmd_camera_linux.dir/clean

CMakeFiles/sample_cmd_camera_linux.dir/depend:
	cd /home/<USER>/AC20_SDK/linux_sdk/libir_sample/simple_sample/cmd_camera/linux/v4l2_i2c && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/AC20_SDK/linux_sdk/libir_sample/simple_sample/cmd_camera/linux/v4l2_i2c /home/<USER>/AC20_SDK/linux_sdk/libir_sample/simple_sample/cmd_camera/linux/v4l2_i2c /home/<USER>/AC20_SDK/linux_sdk/libir_sample/simple_sample/cmd_camera/linux/v4l2_i2c /home/<USER>/AC20_SDK/linux_sdk/libir_sample/simple_sample/cmd_camera/linux/v4l2_i2c /home/<USER>/AC20_SDK/linux_sdk/libir_sample/simple_sample/cmd_camera/linux/v4l2_i2c/CMakeFiles/sample_cmd_camera_linux.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/sample_cmd_camera_linux.dir/depend

