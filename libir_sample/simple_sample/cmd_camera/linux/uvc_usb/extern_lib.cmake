set(PLATFORM_NAME aarch64-linux-gnu)
set(CMAKE_SYSTEM_NAME Linux)
set(TOOLCHAIN_PATH /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/bin)
set(CMAKE_C_COMPILER /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc)
set(CMAKE_C_FLAGS -I/include)
set(CMAKE_CXX_COMPILER /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/bin/aarch64-linux-gnu-g++)
set(CMAKE_FIND_ROOT_PATH )
set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)
list(APPEND CMAKE_PREFIX_PATH )
