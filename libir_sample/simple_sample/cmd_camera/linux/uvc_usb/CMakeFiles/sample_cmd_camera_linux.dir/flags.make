# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# compile CXX with /opt/vs-linux/x86-arm/gcc-linaro-7.5.0-aarch64-linux-gnu/bin/aarch64-linux-gnu-g++
CXX_DEFINES = 

CXX_INCLUDES = -I/home/<USER>/AC20_SDK/linux_sdk/libir_sample/simple_sample/cmd_camera/linux/uvc_usb/../../../../../libir_SDK_release/include -I/home/<USER>/AC20_SDK/linux_sdk/libir_sample/simple_sample/cmd_camera/linux/uvc_usb/../../../../interfaces -I/home/<USER>/AC20_SDK/linux_sdk/libir_sample/simple_sample/cmd_camera/linux/uvc_usb/../../../../drivers

CXX_FLAGS = 

