cmake_minimum_required(VERSION 2.6)
project(cmd_camera_linux)

# 指定交叉编译器
set(CMAKE_SYSTEM_NAME Linux)
set(CMAKE_SYSTEM_PROCESSOR aarch64)
set(CMAKE_C_COMPILER aarch64-linux-gnu-gcc)
set(CMAKE_CXX_COMPILER aarch64-linux-gnu-g++)

set(EXTERN_LIB extern_lib.cmake)
if(NOT EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/${EXTERN_LIB})
    file(WRITE ${EXTERN_LIB} "")
endif()
include(${EXTERN_LIB})

# 头文件路径
include_directories(
    ../../../../../libir_SDK_release/include
    ../../../../interfaces/
    ../../../../drivers/
)

# 库文件路径
link_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../../libir_SDK_release/linux/aarch64-linux-gnu
    # 如有其他库目录可继续添加
)

add_executable(sample_cmd_camera_linux ./sample.cpp)

target_link_libraries(sample_cmd_camera_linux ircam ircmd usb-1.0 iruvc -lm)