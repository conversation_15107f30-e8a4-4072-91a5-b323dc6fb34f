# 简介  
当前路径下的示例展示了如何通过机芯（模组）获取温度  
提供两种方式  
1.通过命令的方式直接获取温度 (get_by_command)  
2.通过温度数据帧获取温度 (get_from_frame)  

# 目录结构
```
cmd_camera/
│
├─get_by_command/       // 使用命令，从模组获取指定温度
│
├─get_from_frame/       // 通过温度数据帧，自行处理，获取特定的温度
│
└─README.md
```
# 示例支持的平台及对应的协议
该路径下的示例均在Windows平台上编译和运行  
使用基于usb的uvc协议进行命令通道和视频通道的传输  
如果需要在其他平台编译和运行，请参考该示例和基础的cmd与cmd_camera示例进行修改和配置脚本  

> **WARNING**: 只有支持测温的模组才可以正常使用该示例，成像版模组请勿使用  
> **WARNING**: get_by_commad示例必须在出图的情况下才能使用，如果在停图情况下使用，会出现异常情况  
> **WARNING**: get_from_frame示例必须在出双图或出温度图的情况下才能使用  
